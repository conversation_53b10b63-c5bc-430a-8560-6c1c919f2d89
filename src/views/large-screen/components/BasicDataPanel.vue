<template>
  <!-- 基础数据 -->
  <div class="basic-data-panel">
    <!-- 城市总览信息 - 改为更紧凑的横向布局 -->
    <div v-if="cityData" class="city-overview">
      <div class="city-name">{{ cityData.name }}</div>
      <div class="city-stats">
        <div class="stat-item">
          <span class="label">面积:</span>
          <span class="value">{{ cityData.area || "暂无数据" }}</span>
        </div>
        <div class="stat-item">
          <span class="label">人口:</span>
          <span class="value">{{ cityData.population || "暂无数据" }}</span>
        </div>
      </div>
    </div>

    <div class="data-section">
      <div class="list-container">
        <!-- 表头 -->
        <div class="list-item header">
          <span class="col" title="地区">地区</span>
          <span class="col" title="机动车">机动车</span>
          <span class="col" title="驾驶员">驾驶员</span>
          <!--          <span class="col" title="运输企业">运输企业</span>-->
        </div>
        <!-- 滚动区域 - 增加高度 -->
        <div class="scroll-area">
          <!-- 区县数据 -->
          <div
            v-for="(item, index) in countyData"
            :key="index"
            class="county-item"
          >
            <div class="list-item county-row" @click="handleRegionClick(item)">
              <span class="col county-name" :title="item.name">{{
                item.name
              }}</span>
              <span class="col" :title="formatNumber(item.vehicles)">{{
                formatNumber(item.vehicles)
              }}</span>
              <span class="col" :title="formatNumber(item.drivers)">{{
                formatNumber(item.drivers)
              }}</span>
              <!--              <span-->
              <!--                class="col"-->
              <!--                :title="formatNumber(item.transportCompanies)"-->
              <!--                >{{ formatNumber(item.transportCompanies) }}</span-->
              <!--              >-->
            </div>
          </div>

          <!-- 无数据提示 -->
          <div v-if="countyData.length === 0" class="no-data">暂无数据</div>
        </div>
      </div>
    </div>

    <!-- 地区详情弹窗 -->
    <RegionDetailDialog
      v-model="showRegionDialog"
      :region-data="selectedRegionData"
      @closed="handleDialogClose"
    />
  </div>
</template>

<script setup lang="ts">
import { useAppStoreHook } from "@/store/modules/app";
import { ref, watch, onMounted, computed } from "vue";
import { getRegionData } from "@/api/largeScreen";
import { useUserStoreHook } from "@/store/modules/user";
import RegionDetailDialog from "../dialogs/RegionDetailDialog.vue";

// 数据类型定义
interface CityData {
  name: string;
  totalVehicles: number;
  totalDrivers: number;
  totalTransportCompanies: number;
  area?: string;
  population?: string;
}

interface CountyData {
  name: string;
  vehicles: number;
  drivers: number;
  transportCompanies: number;
  area?: string;
  population?: string;
  // 扩展字段用于弹窗显示
  nonMotorVehicles: number;
  keyFreightCompanies: number;
  roads?: {
    national: number;
    provincial: number;
    county: number;
    township: number;
    village: number;
    external: number;
  };
}

// 添加轮询相关的代码
let pollingTimer: NodeJS.Timeout | null = null;
const userStore = useUserStoreHook();
const appStore = useAppStoreHook();
// 区县数据
const countyData = ref<CountyData[]>([]);
const cityData = ref<CityData | null>(null);

// 弹窗相关
const showRegionDialog = ref(false);
const selectedRegionData = ref<CountyData | null>(null);

// 模拟数据
const mockCityData: CityData = {
  name: "宜宾市",
  totalVehicles: 156789,
  totalDrivers: 234567,
  totalTransportCompanies: 1245,
  area: "13,283 km²",
  population: "458.88万人"
};

const mockCountyData: CountyData[] = [
  {
    name: "翠屏区",
    vehicles: 45678,
    drivers: 67890,
    transportCompanies: 234,
    area: "1123.45 km²",
    population: "89.5万人",
    nonMotorVehicles: 23456,
    keyFreightCompanies: 45,
    roads: {
      national: 125,
      provincial: 234,
      county: 156,
      township: 89,
      village: 67,
      external: 23
    }
  },
  {
    name: "叙州区",
    vehicles: 38456,
    drivers: 56789,
    transportCompanies: 189,
    area: "2945.67 km²",
    population: "76.8万人",
    nonMotorVehicles: 19876,
    keyFreightCompanies: 38,
    roads: {
      national: 98,
      provincial: 187,
      county: 134,
      township: 76,
      village: 54,
      external: 19
    }
  },
  {
    name: "南溪区",
    vehicles: 23456,
    drivers: 34567,
    transportCompanies: 145,
    area: "704.89 km²",
    population: "45.2万人",
    nonMotorVehicles: 12345,
    keyFreightCompanies: 29,
    roads: {
      national: 67,
      provincial: 123,
      county: 89,
      township: 45,
      village: 32,
      external: 12
    }
  },
  {
    name: "江安县",
    vehicles: 18765,
    drivers: 28901,
    transportCompanies: 123,
    area: "888.12 km²",
    population: "56.7万人",
    nonMotorVehicles: 9876,
    keyFreightCompanies: 24,
    roads: {
      national: 54,
      provincial: 98,
      county: 67,
      township: 34,
      village: 23,
      external: 9
    }
  },
  {
    name: "长宁县",
    vehicles: 15432,
    drivers: 23456,
    transportCompanies: 98,
    area: "999.78 km²",
    population: "43.9万人",
    nonMotorVehicles: 8123,
    keyFreightCompanies: 19,
    roads: {
      national: 43,
      provincial: 76,
      county: 54,
      township: 28,
      village: 19,
      external: 7
    }
  },
  {
    name: "高县",
    vehicles: 12345,
    drivers: 18765,
    transportCompanies: 87,
    area: "1323.45 km²",
    population: "51.2万人",
    nonMotorVehicles: 6543,
    keyFreightCompanies: 17,
    roads: {
      national: 38,
      provincial: 65,
      county: 43,
      township: 23,
      village: 16,
      external: 6
    }
  },
  {
    name: "珙县",
    vehicles: 9876,
    drivers: 15432,
    transportCompanies: 76,
    area: "1149.83 km²",
    population: "42.8万人",
    nonMotorVehicles: 5234,
    keyFreightCompanies: 15,
    roads: {
      national: 32,
      provincial: 54,
      county: 36,
      township: 19,
      village: 13,
      external: 5
    }
  },
  {
    name: "筠连县",
    vehicles: 8765,
    drivers: 13456,
    transportCompanies: 65,
    area: "1256.67 km²",
    population: "38.4万人",
    nonMotorVehicles: 4321,
    keyFreightCompanies: 13,
    roads: {
      national: 28,
      provincial: 45,
      county: 31,
      township: 16,
      village: 11,
      external: 4
    }
  },
  {
    name: "兴文县",
    vehicles: 7654,
    drivers: 12345,
    transportCompanies: 54,
    area: "1373.25 km²",
    population: "45.6万人",
    nonMotorVehicles: 3876,
    keyFreightCompanies: 11,
    roads: {
      national: 24,
      provincial: 38,
      county: 26,
      township: 14,
      village: 9,
      external: 3
    }
  },
  {
    name: "屏山县",
    vehicles: 6543,
    drivers: 10234,
    transportCompanies: 43,
    area: "1504.12 km²",
    population: "34.7万人",
    nonMotorVehicles: 3123,
    keyFreightCompanies: 9,
    roads: {
      national: 19,
      provincial: 32,
      county: 22,
      township: 12,
      village: 8,
      external: 3
    }
  }
];

// 使用计算属性获取当前地址
const currentAddress = computed(() => {
  const address = appStore.largeScreenArea || userStore.getUserLocationPath;
  return address;
});

// 获取区域数据
const fetchRegionData = async () => {
  try {
    // 强制等待一下，确保地址已更新
    await new Promise(resolve => setTimeout(resolve, 100));

    // 暂时使用模拟数据，后续可以替换为真实API调用
    // const res = await getRegionData(appStore.getLargeScreenArea);

    // 使用模拟数据
    cityData.value = mockCityData;
    countyData.value = mockCountyData;

    // 如果需要使用真实API，可以取消注释以下代码：
    /*
    const res = await getRegionData(appStore.getLargeScreenArea);
    if (res.code === 200 && res.data) {
      cityData.value = {
        name: res.data.name,
        totalVehicles: res.data.totalVehicles || 0,
        totalDrivers: res.data.totalDrivers || 0,
        totalTransportCompanies: res.data.totalTransportCompanies || 0,
        area: res.data.area,
        population: res.data.population
      };
      countyData.value = res.data.point?.map(item => ({
        name: item.name,
        vehicles: item.vehicles || 0,
        drivers: item.drivers || 0,
        transportCompanies: item.transportCompanies || 0,
        area: item.area,
        population: item.population
      })) || [];
    } else {
      console.warn("API返回数据格式不正确:", res);
      // 清空数据，避免显示旧数据
      countyData.value = [];
      cityData.value = null;
    }
    */
  } catch (error) {
    console.error("获取区域数据失败:", error);
    // 出错时使用模拟数据
    cityData.value = mockCityData;
    countyData.value = mockCountyData;
  }
};

// 保留原来的 watch 作为备份
watch(
  () => appStore.largeScreenArea,
  (newVal, oldVal) => {
    if (newVal) fetchRegionData();
  },
  { immediate: true, deep: true }
);

// 数字格式化函数
const formatNumber = (num: number | undefined | null): string => {
  if (num === undefined || num === null) return "0";
  return num.toLocaleString("zh-CN");
};

// 点击地区显示详情
const handleRegionClick = (region: CountyData) => {
  selectedRegionData.value = region;
  showRegionDialog.value = true;
};

// 关闭弹窗
const handleDialogClose = () => {
  showRegionDialog.value = false;
  selectedRegionData.value = null;
};

// 组件挂载时加载数据
onMounted(() => {
  fetchRegionData();
});
</script>

<style lang="scss" scoped>
.basic-data-panel {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
  min-height: 0; // 添加最小高度

  .city-overview {
    display: flex; // 改为横向布局
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px; // 减小内边距
    background: rgb(0 123 255 / 10%);
    border-left: 3px solid #007bff;
    border-radius: 4px;

    .city-name {
      margin-right: 10px;
      font-size: 16px; /* 城市总览区域城市名称字体大小 */
      font-weight: 600;
      color: #fff;
    }

    .city-stats {
      display: flex;
      gap: 15px;

      .stat-item {
        display: flex;
        align-items: center;

        .label {
          margin-right: 5px;
          font-size: 16px; /* 城市统计标签("总面积"、"总人口")字体大小 */
          color: rgb(255 255 255 / 70%);
        }

        .value {
          font-size: 16px; /* 城市统计数值字体大小 */
          font-weight: 500;
          color: #29d;
        }
      }
    }
  }

  .data-section {
    display: flex;
    flex: 1;
    flex-direction: column;
    min-height: 0; // 添加最小高度
  }

  .list-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    min-height: 0; // 添加最小高度
    overflow: hidden;
    background: rgb(0 0 0 / 20%);
    border: 1px solid rgb(255 255 255 / 12%);
    border-radius: 4px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);

    .scroll-area {
      flex: 1;
      min-height: 0; // 确保最小高度
      padding-bottom: 8px; // 添加底部内边距
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 4px; // 减小滚动条宽度
        background: rgb(0 0 0 / 20%);
      }

      &::-webkit-scrollbar-thumb {
        background: rgb(255 255 255 / 20%);
        border-radius: 2px;

        &:hover {
          background: rgb(255 255 255 / 30%);
        }
      }
    }
  }

  .list-item {
    display: flex;
    align-items: center;
    padding: 8px 12px; // 减小内边距
    color: rgb(255 255 255 / 80%);
    border-bottom: 1px solid rgb(255 255 255 / 8%);
    transition: all 0.3s;

    &:last-child {
      border-bottom: none;
    }

    .col {
      flex: 1;
      min-width: 0;
      padding: 0 8px;
      overflow: hidden;
      font-size: 18px; /* 数据列表项内容字体大小 */
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .header {
    position: sticky;
    top: 0;
    z-index: 1;
    padding: 12px; /* 增加内边距以适应更大的字体 */
    font-size: 18px; /* 数据列表表头("地区"、"面积"、"人口"、"机动车数")字体大小 */
    font-weight: 500;
    color: rgb(255 255 255 / 80%);
    letter-spacing: 0.5px;
    background: rgb(0 123 255 / 15%);
  }

  .county-row {
    background: rgb(255 255 255 / 2%);
    transition: all 0.2s ease;
    cursor: pointer;

    &:hover {
      background: rgb(0 123 255 / 15%);
      transform: translateX(5px);
      box-shadow: 0 2px 8px rgb(0 123 255 / 20%);
    }

    .county-name {
      font-weight: 500;
      color: #29d;
    }
  }

  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;
    font-size: 1.1rem; /* 无数据状态提示文字字体大小 */
    color: rgb(255 255 255 / 50%);
  }
}
</style>
