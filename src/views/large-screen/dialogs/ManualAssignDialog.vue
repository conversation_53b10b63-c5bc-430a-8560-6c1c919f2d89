<template>
  <!-- 人工下派弹窗 -->
  <teleport to="body">
    <div
      v-if="dialogVisible"
      class="dialog-container"
      @click.self="handleClose"
    >
      <div class="dialog-content">
        <dv-border-box-12
          class="dialog-border"
          style="background: transparent !important"
        >
          <!-- 标题栏 -->
          <div class="dialog-header">
            <div class="title">
              <dv-decoration-5 style="width: 60px; height: 30px" />
              <span class="title-text">人工下派</span>
            </div>
            <div class="large-screen-close-btn" @click="handleClose">
              <el-icon><Close /></el-icon>
            </div>
          </div>

          <!-- 内容区 -->
          <div class="dialog-body">
            <div class="dispatch-form">
              <!-- 违法信息卡片 -->
              <div class="info-card">
                <div class="card-title">
                  <span class="title-text">违法信息</span>
                </div>

                <div class="form-item">
                  <span class="label required">违法名称</span>
                  <el-input
                    v-model="form.illegalName"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入违法名称"
                    maxlength="200"
                    show-word-limit
                    class="large-screen-input"
                  />
                </div>

                <div class="form-item">
                  <span class="label required">违法证据</span>
                  <el-upload
                    v-model:file-list="files"
                    list-type="picture-card"
                    :auto-upload="false"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemove"
                    :before-upload="beforeUpload"
                    :http-request="customUpload"
                    class="large-screen-upload"
                  >
                    <el-icon><Plus /></el-icon>
                    <template #tip>
                      <div class="el-upload__tip">只能上传jpg/png文件，且不超过5MB</div>
                    </template>
                  </el-upload>
                </div>
              </div>

              <!-- 分派信息卡片 -->
              <div class="form-card">
                <div class="card-title">
                  <span class="title-text">分派信息</span>
                </div>

                <!-- 违法地点 -->
                <div class="form-item">
                  <span class="label required">违法地点</span>
                  <el-cascader
                    v-model="form.locationIds"
                    :options="userStore.getUserRegionTree"
                    :props="{
                      expandTrigger: 'hover',
                      checkStrictly: true,
                      label: 'label',
                      value: 'id',
                      children: 'childList'
                    }"
                    placeholder="选择违法地点"
                    clearable
                    class="large-screen-cascader"
                    @change="handleLocationChange"
                  />
                </div>

                <!-- 处理人员 -->
                <div v-if="staffList.length > 0" class="form-item">
                  <span class="label required">处理人员</span>
                  <el-select
                    v-model="selectedStaffId"
                    placeholder="请选择处理人员"
                    class="large-screen-select"
                    @change="handleStaffChange"
                  >
                    <el-option
                      v-for="option in staffList"
                      :key="option.userId"
                      :label="option.name"
                      :value="option.userId"
                    />
                  </el-select>
                </div>

                <!-- 处理期限 -->
                <div class="form-item">
                  <span class="label required">处理期限</span>
                  <el-select
                    v-model="selectedDeadline"
                    placeholder="请选择处理期限"
                    class="large-screen-select"
                    @change="handleDeadlineChange"
                  >
                    <el-option
                      v-for="option in deadlineOptions"
                      :key="option.value"
                      :label="option.text"
                      :value="option.value"
                    />
                  </el-select>
                </div>
              </div>

              <!-- 提交按钮 -->
              <div class="submit-section">
                <el-button
                  type="primary"
                  class="submit-btn"
                  :loading="submitLoading"
                  @click="handleSubmit"
                >
                  确认分派
                </el-button>
              </div>
            </div>
          </div>
        </dv-border-box-12>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts" name="ManualAssignDialog">
import { dispatchIllegal } from "@/api/illegal";
import { getUserList } from "@/api/system";
import { useUserStoreHook } from "@/store/modules/user";
import type { AddressLevel } from "@/types/business";
import type { TreeNode, User } from "@/views/system/system-user/utils/types";
import type {
  UploadFile,
  UploadFiles,
  UploadRawFile,
  UploadUserFile
} from "element-plus";
import { ElLoading, ElMessage } from "element-plus";
import { Close, Plus } from "@element-plus/icons-vue";
import { onMounted, reactive, ref, watch } from "vue";

// 类型定义
interface DispatchForm extends LocationPath {
  illegalName: string;
  userIds: string;
  userNames: string;
  deadlineTime: string;
  locationIds: number[];
  pictureUrls: string[];
}

interface LocationPath {
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
}

interface DeadlineOption {
  text: string;
  value: number;
}

// Props
const props = defineProps<{
  modelValue: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
  (e: "closed"): void;
  (e: "success"): void;
}>();

// 弹窗显示状态
const dialogVisible = ref(props.modelValue);
const submitLoading = ref(false);
const userStore = useUserStoreHook();

// 表单数据
const form = reactive<DispatchForm>({
  illegalName: "",
  userIds: "",
  userNames: "",
  deadlineTime: "",
  locationIds: [],
  pictureUrls: []
});

const selectedDeadline = ref<number>();
const selectedStaffId = ref<number>();

// 状态数据
const files = ref<UploadUserFile[]>([]);
const staffList = ref<User[]>([]);

// 处理期限选项
const deadlineOptions: DeadlineOption[] = [
  { text: "1天", value: 1 },
  { text: "3天", value: 3 },
  { text: "5天", value: 5 },
  { text: "7天", value: 7 }
];

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  newValue => {
    dialogVisible.value = newValue;
    if (newValue) {
      // 弹窗打开时重置表单
      resetForm();
    }
  }
);

onMounted(() => {
  userStore.initUserRegionTree();
});

// 重置表单
const resetForm = () => {
  Object.keys(form).forEach(key => {
    if (typeof form[key] === "object") {
      form[key] = [];
    } else {
      form[key] = "";
    }
  });
  files.value = [];
  selectedDeadline.value = undefined;
  selectedStaffId.value = undefined;
  staffList.value = [];
};

// 图片上传相关方法
const handlePictureCardPreview = (file: UploadFile) => {
  window.open(file.url);
};

const handleRemove = (file: UploadFile, fileList: UploadFiles) => {
  files.value = fileList as UploadUserFile[];
};

const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith("image/");
  if (!isImage) {
    ElMessage.error("只能上传图片文件!");
    return false;
  }
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    ElMessage.error("图片大小不能超过 5MB!");
    return false;
  }
  return true;
};

const customUpload = async (params: { file: File }) => {
  const rawFile = params.file as UploadRawFile;
  rawFile.uid = Date.now();

  const newFile: UploadUserFile = {
    name: params.file.name,
    url: URL.createObjectURL(params.file),
    raw: rawFile
  };
  files.value.push(newFile);
};

// 处理人员变化
const handleStaffChange = (v: number) => {
  form.userIds = v.toString();
  const staffItem = staffList.value.find(staff => staff.userId === v);
  form.userNames = staffItem?.name || "";
};

// 处理地点选择变化
const handleLocationChange = (value: number[]) => {
  if (value?.length) {
    const areaPath = getAddressParams(value);
    Object.keys(areaPath).forEach(key => {
      form[key] = areaPath[key];
    });
    getUserList({
      curPage: 1,
      pageSize: 1000,
      ...areaPath
    }).then(res => {
      if (res.code === 200) {
        staffList.value = res.data.records;
      }
    });
  } else {
    staffList.value = [];
    form.userIds = "";
    form.userNames = "";
    selectedStaffId.value = undefined;
  }
};

// 处理期限选择变化
const handleDeadlineChange = (days: number) => {
  const deadline = new Date();
  deadline.setDate(deadline.getDate() + days);
  deadline.setHours(23, 59, 59);
  form.deadlineTime = formatDateTime(deadline);
};

const formatDateTime = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 获取地址参数
const getAddressParams = (areaIds: number[]): AddressLevel => {
  const params: AddressLevel = {
    city: userStore.userInfo.city
  };
  if (!areaIds?.length) return params;

  let currentNode = userStore.userRegionTree.find(
    item => item.id === areaIds[0]
  );
  if (!currentNode) return {};

  const levels = ["county", "township", "hamlet", "site"] as const;
  for (let i = 0; i < areaIds.length; i++) {
    const id = areaIds[i + 1];
    if (!currentNode) break;

    const level = levels[i];
    params[level] = currentNode.label;
    currentNode = findNodeById(currentNode.childList, id);
  }
  return params;
};

// 根据ID查找节点
const findNodeById = (
  nodes: TreeNode[] | null,
  id: number
): TreeNode | null => {
  if (!nodes) return null;

  for (const node of nodes) {
    if (node.id === id) return node;
    const found = findNodeById(node.childList, id);
    if (found) return found;
  }
  return null;
};

// 表单验证
const validateForm = () => {
  if (!form.illegalName) {
    ElMessage.warning("请输入违法说明");
    return false;
  }
  if (!files.value.length) {
    ElMessage.warning("请上传违法证据图片");
    return false;
  }
  if (!form.locationIds.length) {
    ElMessage.warning("请选择违法地点");
    return false;
  }
  if (!form.userIds) {
    ElMessage.warning("请选择处理人员");
    return false;
  }
  if (!form.deadlineTime) {
    ElMessage.warning("请选择处理期限");
    return false;
  }
  return true;
};

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) return;

  submitLoading.value = true;

  try {
    const formData = new FormData();

    // 处理文件上传
    files.value.forEach(file => {
      if (file.raw) {
        formData.append("files", file.raw);
      }
    });

    // 添加其他表单数据
    formData.append("IllegalName", form.illegalName);
    formData.append("userIds", form.userIds);
    formData.append("userNames", form.userNames);
    formData.append("termTime", form.deadlineTime);

    // 添加地点信息
    formData.append("city", form.city || "");
    formData.append("county", form.county || "");
    formData.append("township", form.township || "");
    formData.append("hamlet", form.hamlet || "");
    formData.append("site", form.site || "");

    const res = await dispatchIllegal(formData);

    if (res.code === 200) {
      ElMessage.success("违法分派成功");
      emit("success");
      handleClose();
    } else {
      ElMessage.error("分派失败");
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("提交失败，请重试");
  } finally {
    submitLoading.value = false;
  }
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  emit("update:modelValue", false);
  emit("closed");
};
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";

.dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background: rgb(0 10 30 / 80%);
  backdrop-filter: blur(8px);
}

.dialog-content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(var(--scale-ratio));
  width: 1792px;
  height: 1024px;
}

.dialog-border {
  width: 100%;
  height: 100%;
  padding: 30px;
}

// 确保 dv-border-box-12 组件背景透明
:deep(.dv-border-box-12) {
  background: transparent !important;

  .border-box-content {
    background: transparent !important;
  }

  svg {
    background: transparent !important;
  }
}

.dialog-header {
  height: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
  border-bottom: 1px solid rgba(64, 158, 255, 0.2);

  .title {
    display: flex;
    align-items: center;
    gap: 15px;

    .title-text {
      @extend .fs-dialog-title;
      font-weight: 600;
      color: #fff;
      text-shadow: 0 0 10px rgb(64 158 255 / 50%);
    }
  }

  // 关闭按钮样式现在使用统一的 .large-screen-close-btn 类
}

.dialog-body {
  flex: 1;
  padding: 20px 30px;
  position: relative;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.dispatch-form {
  display: flex;
  flex-direction: column;
  gap: 30px;
  height: 100%;
}

.info-card,
.form-card {
  padding: 25px;
  background: rgba(0, 20, 40, 0.6);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.card-title {
  padding-bottom: 16px;
  margin-bottom: 24px;
  border-bottom: 1px solid rgba(64, 158, 255, 0.2);

  .title-text {
    position: relative;
    padding-left: 16px;
    @extend .fs-dialog-content;
    font-weight: 600;
    color: #fff;
    text-shadow: 0 0 8px rgba(64, 158, 255, 0.5);

    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 4px;
      height: 18px;
      content: "";
      background: linear-gradient(135deg, #409eff, #67c23a);
      border-radius: 2px;
      transform: translateY(-50%);
    }
  }
}

.form-item {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    display: block;
    margin-bottom: 12px;
    @extend .fs-dialog-content;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;

    &.required::before {
      margin-right: 6px;
      color: #f56c6c;
      content: "*";
    }
  }
}

.submit-section {
  display: flex;
  justify-content: center;
  padding-top: 20px;
  margin-top: auto;
}

.submit-btn {
  min-width: 160px;
  height: 48px;
  @extend .fs-dialog-content;
  font-weight: 600;
  background: linear-gradient(135deg, #409eff, #67c23a);
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
}

// 大屏表单元素样式
:deep(.large-screen-input) {
  .el-input__wrapper {
    background: rgba(0, 30, 60, 0.8);
    border: 1px solid rgba(64, 158, 255, 0.3);
    border-radius: 8px;
    box-shadow: none;
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(64, 158, 255, 0.6);
    }

    &.is-focus {
      border-color: #409eff;
      box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
    }
  }

  .el-input__inner,
  .el-textarea__inner {
    color: #fff;
    background: transparent;

    &::placeholder {
      color: rgba(255, 255, 255, 0.5);
    }
  }

  .el-input__count {
    color: rgba(255, 255, 255, 0.6);
    background: transparent;
  }
}

:deep(.large-screen-select) {
  --el-input-bg-color: rgba(0, 30, 60, 0.8);
  --el-input-border-color: rgba(64, 158, 255, 0.3);
  --el-input-hover-border-color: rgba(64, 158, 255, 0.6);
  --el-input-focus-border-color: #409eff;
  --el-input-text-color: #ffffff;
  --el-input-placeholder-color: rgba(255, 255, 255, 0.5);

  .el-select__wrapper {
    background-color: var(--el-input-bg-color);
    border: 1px solid var(--el-input-border-color);
    border-radius: 8px;
    box-shadow: none;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--el-input-hover-border-color);
    }

    &.is-focus {
      border-color: var(--el-input-focus-border-color);
      box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
    }
  }

  .el-select__caret {
    color: rgba(255, 255, 255, 0.6) !important;
  }

  .el-input__inner {
    color: var(--el-input-text-color) !important;

    &::placeholder {
      color: var(--el-input-placeholder-color) !important;
    }
  }

  .el-select__selected-item {
    color: var(--el-input-text-color) !important;
  }

  .el-select__selection {
    color: var(--el-input-text-color) !important;
  }
}

:deep(.large-screen-cascader) {
  --el-input-bg-color: rgba(0, 30, 60, 0.8);
  --el-input-border-color: rgba(64, 158, 255, 0.3);
  --el-input-hover-border-color: rgba(64, 158, 255, 0.6);
  --el-input-focus-border-color: #409eff;
  --el-input-text-color: #ffffff;
  --el-input-placeholder-color: rgba(255, 255, 255, 0.5);

  .el-cascader {
    width: 100%;
  }

  .el-input__wrapper {
    background-color: var(--el-input-bg-color);
    border: 1px solid var(--el-input-border-color);
    border-radius: 8px;
    box-shadow: none;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--el-input-hover-border-color);
    }

    &.is-focus {
      border-color: var(--el-input-focus-border-color);
      box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
    }
  }

  .el-input__inner {
    background: transparent;
    color: var(--el-input-text-color) !important;

    &::placeholder {
      color: var(--el-input-placeholder-color) !important;
    }
  }

  .el-input__suffix-inner {
    color: rgba(255, 255, 255, 0.6) !important;
  }

  .el-cascader__label {
    color: var(--el-input-text-color) !important;
  }
}

:deep(.large-screen-upload) {
  .el-upload--picture-card {
    width: 120px;
    height: 120px;
    background: rgba(0, 30, 60, 0.8);
    border: 2px dashed rgba(64, 158, 255, 0.3);
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(64, 158, 255, 0.6);
      background: rgba(0, 30, 60, 0.9);
    }

    .el-icon {
      color: rgba(64, 158, 255, 0.8);
      font-size: 28px;
    }
  }

  .el-upload-list--picture-card {
    .el-upload-list__item {
      background: rgba(0, 30, 60, 0.8);
      border: 1px solid rgba(64, 158, 255, 0.3);
      border-radius: 8px;
    }
  }

  .el-upload__tip {
    color: rgba(255, 255, 255, 0.6);
    margin-top: 8px;
    font-size: 14px;
  }
}

// 确保下拉菜单在弹窗中正常显示
:deep(.el-cascader__dropdown),
:deep(.el-popper),
:deep(.el-picker__popper),
:deep(.el-select__popper) {
  z-index: 10002 !important;
  background: rgba(0, 20, 40, 0.95) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5) !important;

  .el-cascader-menu {
    background: transparent !important;
    border-right: 1px solid rgba(64, 158, 255, 0.2) !important;

    .el-cascader-node {
      color: rgba(255, 255, 255, 0.95) !important;
      background: transparent !important;

      &:hover {
        background: rgba(64, 158, 255, 0.25) !important;
        color: #ffffff !important;
      }

      &.is-active {
        background: rgba(64, 158, 255, 0.4) !important;
        color: #ffffff !important;
        font-weight: 600 !important;
      }

      &.in-active-path {
        background: rgba(64, 158, 255, 0.2) !important;
        color: #ffffff !important;
      }
    }
  }

  .el-select-dropdown__item {
    color: rgba(255, 255, 255, 0.95) !important;
    background: transparent !important;

    &:hover {
      background: rgba(64, 158, 255, 0.25) !important;
      color: #ffffff !important;
    }

    &.is-selected {
      background: rgba(64, 158, 255, 0.4) !important;
      color: #ffffff !important;
      font-weight: 600 !important;
    }
  }

  .el-scrollbar__view {
    background: transparent !important;
  }
}

// 全局覆盖下拉菜单样式，确保在大屏弹窗中正确显示
:global(.el-cascader__dropdown),
:global(.el-popper),
:global(.el-picker__popper),
:global(.el-select__popper),
:global(.el-autocomplete-suggestion) {
  &[data-popper-placement] {
    z-index: 19999 !important;
    background: rgba(0, 20, 40, 0.95) !important;
    border: 1px solid rgba(64, 158, 255, 0.3) !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5) !important;

    .el-cascader-menu {
      background: transparent !important;

      .el-cascader-node {
        color: rgba(255, 255, 255, 0.95) !important;
        background: transparent !important;

        &:hover {
          background: rgba(64, 158, 255, 0.25) !important;
          color: #ffffff !important;
        }

        &.is-active,
        &.in-active-path {
          background: rgba(64, 158, 255, 0.4) !important;
          color: #ffffff !important;
          font-weight: 600 !important;
        }
      }
    }

    .el-select-dropdown__item {
      color: rgba(255, 255, 255, 0.95) !important;
      background: transparent !important;

      &:hover {
        background: rgba(64, 158, 255, 0.25) !important;
        color: #ffffff !important;
      }

      &.is-selected {
        background: rgba(64, 158, 255, 0.4) !important;
        color: #ffffff !important;
        font-weight: 600 !important;
      }
    }
  }
}
</style>

<style lang="scss">
// 全局强制覆盖下拉菜单样式 - 不使用 scoped
.el-cascader__dropdown,
.el-popper,
.el-picker__popper,
.el-select__popper {
  z-index: 19999 !important;
  background: rgba(0, 20, 40, 0.95) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5) !important;

  .el-cascader-menu {
    background: transparent !important;
    border-right: 1px solid rgba(64, 158, 255, 0.2) !important;

    .el-cascader-node {
      color: rgba(255, 255, 255, 0.95) !important;
      background: transparent !important;

      &:hover {
        background: rgba(64, 158, 255, 0.25) !important;
        color: #ffffff !important;
      }

      &.is-active {
        background: rgba(64, 158, 255, 0.4) !important;
        color: #ffffff !important;
        font-weight: 600 !important;
      }

      &.in-active-path {
        background: rgba(64, 158, 255, 0.2) !important;
        color: #ffffff !important;
      }
    }
  }

  .el-select-dropdown__item {
    color: rgba(255, 255, 255, 0.95) !important;
    background: transparent !important;

    &:hover {
      background: rgba(64, 158, 255, 0.25) !important;
      color: #ffffff !important;
    }

    &.is-selected {
      background: rgba(64, 158, 255, 0.4) !important;
      color: #ffffff !important;
      font-weight: 600 !important;
    }
  }

  .el-scrollbar__view {
    background: transparent !important;
  }
}

// 特别针对级联选择器的强制样式
.el-cascader-panel {
  background: rgba(0, 20, 40, 0.95) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;

  .el-cascader-menu {
    background: transparent !important;

    .el-cascader-node {
      color: rgba(255, 255, 255, 0.95) !important;
      background: transparent !important;

      &:hover {
        background: rgba(64, 158, 255, 0.25) !important;
        color: #ffffff !important;
      }

      &.is-active,
      &.in-active-path {
        background: rgba(64, 158, 255, 0.4) !important;
        color: #ffffff !important;
        font-weight: 600 !important;
      }
    }
  }
}

// 强制修复选择框内部文字颜色
.large-screen-select {
  .el-input__inner {
    color: #ffffff !important;
  }

  .el-select__selected-item {
    color: #ffffff !important;
  }

  .el-select__placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
  }

  // 修复选择框的输入区域文字颜色
  .el-input {
    color: #ffffff !important;

    input {
      color: #ffffff !important;
    }
  }

  // 修复选择框包装器内的文字
  .el-select__wrapper {
    color: #ffffff !important;

    .el-select__selection {
      color: #ffffff !important;
    }
  }
}

.large-screen-cascader {
  .el-input__inner {
    color: #ffffff !important;
  }

  .el-cascader__label {
    color: #ffffff !important;
  }

  // 修复级联选择器的输入区域
  .el-input {
    color: #ffffff !important;

    input {
      color: #ffffff !important;
    }
  }

  // 修复级联选择器的标签显示
  .el-cascader__tags {
    color: #ffffff !important;
  }
}

// 最高优先级强制覆盖 - 区分占位符和选中内容
.dialog-container .large-screen-select .el-input__wrapper .el-input__inner,
.dialog-container .large-screen-select .el-select__wrapper .el-input__inner,
.dialog-container .large-screen-cascader .el-input__wrapper .el-input__inner {
  // 占位符状态保持灰色
  &:placeholder-shown {
    color: rgba(255, 255, 255, 0.5) !important;
  }

  // 有内容时显示白色
  &:not(:placeholder-shown) {
    color: #ffffff !important;
  }

  // 占位符文字颜色
  &::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
  }
}

// 选中项始终显示白色
.dialog-container .large-screen-select .el-select__selected-item,
.dialog-container .large-screen-select .el-select__selection {
  color: #ffffff !important;
}

.dialog-container .large-screen-cascader .el-cascader__label {
  color: #ffffff !important;
}

// 使用属性选择器区分状态
.large-screen-select input[class*="el-input__inner"],
.large-screen-cascader input[class*="el-input__inner"] {
  // 占位符状态
  &:placeholder-shown {
    color: rgba(255, 255, 255, 0.5) !important;
  }

  // 有值状态
  &:not(:placeholder-shown) {
    color: #ffffff !important;
  }
}
</style>
