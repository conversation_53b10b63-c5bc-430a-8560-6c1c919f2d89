<template>
  <!-- 地区详情弹窗 -->
  <teleport to="body">
    <div
      v-if="dialogVisible"
      class="dialog-container"
      @click.self="handleClose"
    >
      <div class="dialog-content">
        <dv-border-box-12
          class="dialog-border"
          style="background: transparent !important"
        >
          <!-- 标题栏 -->
          <div class="dialog-header">
            <div class="title">
              <dv-decoration-5 style="width: 60px; height: 30px" />
              <span class="title-text">{{
                regionData?.name || "地区详情"
              }}</span>
            </div>
            <div class="large-screen-close-btn" @click="handleClose">
              <el-icon><Close /></el-icon>
            </div>
          </div>

          <!-- 内容区 -->
          <div class="dialog-body">
            <div v-if="regionData" class="region-detail-content">
              <!-- 基础信息 -->
              <div class="info-section">
                <div class="section-title">基础信息</div>
                <div class="info-grid">
                  <div class="info-item">
                    <span class="label">地区名称:</span>
                    <span class="value">{{ regionData.name }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">总面积:</span>
                    <span class="value">{{
                      regionData.area || "暂无数据"
                    }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">总人口:</span>
                    <span class="value">{{
                      regionData.population || "暂无数据"
                    }}</span>
                  </div>
                </div>
              </div>

              <!-- 交通数据 -->
              <div class="info-section">
                <div class="section-title">交通数据</div>
                <div class="info-grid">
                  <div class="info-item">
                    <span class="label">机动车:</span>
                    <span class="value highlight"
                      >{{ formatNumber(regionData.vehicles) }} 辆</span
                    >
                  </div>
                  <div class="info-item">
                    <span class="label">非机动车:</span>
                    <span class="value highlight"
                      >{{ formatNumber(regionData.nonMotorVehicles) }} 辆</span
                    >
                  </div>
                  <div class="info-item">
                    <span class="label">驾驶员:</span>
                    <span class="value highlight"
                      >{{ formatNumber(regionData.drivers) }} 人</span
                    >
                  </div>
                </div>
              </div>

              <!-- 道路信息 -->
              <div class="info-section">
                <div class="section-title">道路通车里程</div>
                <div class="road-grid">
                  <div class="road-item">
                    <span class="road-type">国道</span>
                    <span class="road-length"
                      >{{ formatNumber(regionData.roads?.national) }} 公里</span
                    >
                  </div>
                  <div class="road-item">
                    <span class="road-type">省道</span>
                    <span class="road-length"
                      >{{
                        formatNumber(regionData.roads?.provincial)
                      }}
                      公里</span
                    >
                  </div>
                  <div class="road-item">
                    <span class="road-type">县道</span>
                    <span class="road-length"
                      >{{ formatNumber(regionData.roads?.county) }} 公里</span
                    >
                  </div>
                  <div class="road-item">
                    <span class="road-type">乡道</span>
                    <span class="road-length"
                      >{{ formatNumber(regionData.roads?.township) }} 公里</span
                    >
                  </div>
                  <div class="road-item">
                    <span class="road-type">村道</span>
                    <span class="road-length"
                      >{{ formatNumber(regionData.roads?.village) }} 公里</span
                    >
                  </div>
                  <div class="road-item">
                    <span class="road-type">库外路</span>
                    <span class="road-length"
                      >{{ formatNumber(regionData.roads?.external) }} 公里</span
                    >
                  </div>
                </div>
              </div>

              <!-- 企业信息 -->
              <div class="info-section">
                <div class="section-title">企业信息</div>
                <div class="info-grid">
                  <div class="info-item">
                    <span class="label">运输企业:</span>
                    <span class="value highlight"
                      >{{
                        formatNumber(regionData.transportCompanies)
                      }}
                      家</span
                    >
                  </div>
                  <div class="info-item">
                    <span class="label">重点货运源头企业:</span>
                    <span class="value highlight"
                      >{{
                        formatNumber(regionData.keyFreightCompanies)
                      }}
                      家</span
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- 无数据状态 -->
            <div v-else class="content-placeholder">
              <div class="placeholder-icon">
                <el-icon size="64">
                  <svg
                    viewBox="0 0 1024 1024"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
                    />
                    <path
                      d="M464 336a48 48 0 1 0 96 0 48 48 0 1 0-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z"
                    />
                  </svg>
                </el-icon>
              </div>
              <div class="placeholder-text">
                <h3>暂无数据</h3>
                <p>该地区的详细信息暂时无法获取</p>
              </div>
            </div>
          </div>
        </dv-border-box-12>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts" name="RegionDetailDialog">
import { ref, watch } from "vue";
import { Close } from "@element-plus/icons-vue";

// 地区详情数据类型
interface RegionDetailData {
  name: string;
  area?: string;
  population?: string;
  vehicles: number;
  nonMotorVehicles: number;
  drivers: number;
  transportCompanies: number;
  keyFreightCompanies: number;
  roads?: {
    national: number;
    provincial: number;
    county: number;
    township: number;
    village: number;
    external: number;
  };
}

// Props
const props = defineProps<{
  modelValue: boolean;
  regionData?: RegionDetailData | null;
}>();

// Emits
const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
  (e: "closed"): void;
}>();

// 弹窗显示状态
const dialogVisible = ref(props.modelValue);

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  newValue => {
    dialogVisible.value = newValue;
  }
);

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  emit("update:modelValue", false);
  emit("closed");
};

// 数字格式化函数
const formatNumber = (num: number | undefined | null): string => {
  if (num === undefined || num === null) return "0";
  return num.toLocaleString("zh-CN");
};
</script>

<style lang="scss" scoped>
@import "../styles/font-sizes.scss";

.dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background: rgb(0 10 30 / 80%);
  backdrop-filter: blur(8px);
}

.dialog-content {
  width: 70vw;
  height: 70vh;
}

.dialog-border {
  width: 100%;
  height: 100%;
  padding: 30px;
}

// 确保 dv-border-box-12 组件背景透明
:deep(.dv-border-box-12) {
  background: transparent !important;

  .border-box-content {
    background: transparent !important;
  }

  svg {
    background: transparent !important;
  }
}

.dialog-header {
  height: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
  border-bottom: 1px solid rgba(64, 158, 255, 0.2);

  .title {
    display: flex;
    align-items: center;
    gap: 15px;

    .title-text {
      @extend .fs-dialog-title;
      font-weight: 600;
      color: #fff;
      text-shadow: 0 0 10px rgb(64 158 255 / 50%);
    }
  }
}

.dialog-body {
  flex: 1;
  padding: 20px 30px;
  position: relative;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: calc(100% - 70px);
}

.region-detail-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.info-section {
  .section-title {
    font-size: 20px;
    color: #409eff;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(64, 158, 255, 0.3);
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 6px;
  border-left: 3px solid #409eff;

  .label {
    @extend .fs-dialog-content;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
  }

  .value {
    @extend .fs-dialog-content;
    color: #fff;
    font-weight: 600;

    &.highlight {
      color: #67c23a;
    }
  }
}

.road-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.road-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 14px;
  background: rgba(103, 194, 58, 0.1);
  border-radius: 6px;
  border-left: 3px solid #67c23a;

  .road-type {
    @extend .fs-dialog-content;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
  }

  .road-length {
    @extend .fs-dialog-content;
    color: #67c23a;
    font-weight: 600;
  }
}

.content-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;

  .placeholder-icon {
    margin-bottom: 20px;
    color: rgba(64, 158, 255, 0.6);
    opacity: 0.8;

    svg {
      width: 64px;
      height: 64px;
      fill: currentColor;
    }
  }

  .placeholder-text {
    h3 {
      margin: 0 0 10px 0;
      @extend .fs-dialog-title;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
    }

    p {
      margin: 0;
      @extend .fs-dialog-content;
      color: rgba(255, 255, 255, 0.6);
      line-height: 1.6;
    }
  }
}
</style>
