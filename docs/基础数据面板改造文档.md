# 基础数据面板改造文档

## 📋 项目概述

本文档描述了对大屏系统中基础数据面板（BasicDataPanel.vue）的改造，将原有的面积、人口、机动车数据展示改为机动车、驾驶员、运输企业等交通相关数据的展示，并增加了点击地区查看详细信息的功能。

## 🔄 改造内容

### 1. 界面布局调整

#### 城市总览区域
- **保持原有**：显示总面积、总人口
- **位置**：组件顶部横向布局

#### 数据列表表头
- **原来**：地区、面积、人口、机动车数
- **现在**：地区、机动车、驾驶员、运输企业

#### 交互功能
- **新增**：点击地区行显示详细信息弹窗
- **新增**：鼠标悬停效果提示可点击

### 2. 数据结构扩展

#### 城市数据类型 (CityData)
```typescript
interface CityData {
  name: string;                    // 城市名称
  totalVehicles: number;           // 全市机动车总数
  totalDrivers: number;            // 全市驾驶员总数
  totalTransportCompanies: number; // 全市运输企业总数
  area?: string;                   // 总面积
  population?: string;             // 总人口
}
```

#### 区县数据类型 (CountyData)
```typescript
interface CountyData {
  name: string;                    // 区县名称
  vehicles: number;                // 机动车数量
  drivers: number;                 // 驾驶员数量
  transportCompanies: number;      // 运输企业数量
  area?: string;                   // 区县面积
  population?: string;             // 区县人口
  // 扩展字段用于弹窗显示
  nonMotorVehicles?: number;       // 非机动车数量
  keyFreightCompanies?: number;    // 重点货运源头企业数量
  roads?: {                        // 道路通车里程
    national: number;              // 国道里程
    provincial: number;            // 省道里程
    county: number;                // 县道里程
    township: number;              // 乡道里程
    village: number;               // 村道里程
    external: number;              // 库外路里程
  };
}
```

### 3. 新增组件

#### RegionDetailDialog.vue
- **功能**：显示地区详细信息的弹窗组件
- **尺寸**：70vw × 70vh
- **样式**：参考ManualAssignDialog.vue的设计风格
- **内容分区**：
  - 基础信息：地区名称、面积、人口
  - 交通数据：机动车、非机动车、驾驶员
  - 道路里程：六种道路类型的通车里程
  - 企业信息：运输企业、重点货运源头企业

## 📊 数据展示

### 城市总览
```
宜宾市
总面积: 13,283 km²    总人口: 458.88万人
```

### 数据列表示例
| 地区   | 机动车  | 驾驶员  | 运输企业 |
|--------|---------|---------|----------|
| 翠屏区 | 45,678  | 67,890  | 234      |
| 叙州区 | 38,456  | 56,789  | 189      |
| 南溪区 | 23,456  | 34,567  | 145      |

### 弹窗详细信息
点击任意地区后显示：
- **基础信息**：地区名称、总面积、总人口
- **交通数据**：机动车、非机动车、驾驶员数量
- **道路里程**：国道、省道、县道、乡道、村道、库外路
- **企业信息**：运输企业、重点货运源头企业数量

## 🔧 技术实现

### 核心功能
1. **数字格式化**：使用千分位分隔符显示大数字
2. **点击交互**：地区行支持点击查看详情
3. **弹窗管理**：统一的弹窗显示和关闭逻辑
4. **类型安全**：完整的TypeScript类型定义

### 样式特性
1. **悬停效果**：鼠标悬停时背景变蓝、向右移动、显示阴影
2. **响应式设计**：弹窗自适应屏幕尺寸
3. **统一风格**：与其他大屏组件保持一致的视觉风格

## 📡 后端API接口要求

### 接口地址
```
GET /largeScreen/getBasicDataByMap
```

### 请求参数
```typescript
{
  city?: string;
  county?: string;
  township?: string;
  hamlet?: string;
  site?: string;
}
```

### 返回数据格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "name": "宜宾市",
    "area": "13,283 km²",
    "population": "458.88万人",
    "totalVehicles": 156789,
    "totalDrivers": 234567,
    "totalTransportCompanies": 1245,
    "point": [
      {
        "name": "翠屏区",
        "area": "1123.45 km²",
        "population": "89.5万人",
        "vehicles": 45678,
        "drivers": 67890,
        "transportCompanies": 234,
        "nonMotorVehicles": 23456,
        "keyFreightCompanies": 45,
        "roads": {
          "national": 125,
          "provincial": 234,
          "county": 156,
          "township": 89,
          "village": 67,
          "external": 23
        }
      }
    ]
  }
}
```

### 字段说明

#### 必需字段
- `name`: 地区名称
- `vehicles`: 机动车数量
- `drivers`: 驾驶员数量
- `transportCompanies`: 运输企业数量

#### 可选字段
- `area`: 面积（可包含单位）
- `population`: 人口（可包含单位）
- `nonMotorVehicles`: 非机动车数量
- `keyFreightCompanies`: 重点货运源头企业数量
- `roads`: 道路通车里程对象

#### 数据类型要求
- 所有数量字段请返回数字类型
- 面积和人口可以是包含单位的字符串
- 如果某个字段没有数据，可以不返回或返回null

## 🚀 部署和集成

### 当前状态
- ✅ 使用模拟数据，可以正常显示和交互
- ✅ 所有功能已实现并测试通过
- ✅ 样式和布局已优化完成

### 集成真实API
当后端API准备就绪时，需要进行以下操作：

1. **取消注释真实API调用代码**（第247-271行）
```typescript
const res = await getRegionData(appStore.getLargeScreenArea);
if (res.code === 200 && res.data) {
  // 处理真实数据
}
```

2. **注释模拟数据使用**（第243-244行）
```typescript
// cityData.value = mockCityData;
// countyData.value = mockCountyData;
```

3. **验证数据格式**
确保后端返回的数据格式符合上述API接口要求

## 📁 文件清单

### 修改的文件
- `src/views/large-screen/components/BasicDataPanel.vue` - 主要组件文件

### 新增的文件
- `src/views/large-screen/dialogs/RegionDetailDialog.vue` - 地区详情弹窗组件

### 相关文件
- `src/api/largeScreen.ts` - API接口定义
- `src/views/large-screen/styles/font-sizes.scss` - 样式配置

## 🎯 总结

本次改造成功实现了：
1. 保持城市总览显示面积和人口
2. 表格显示机动车、驾驶员、运输企业核心数据
3. 点击地区查看包含道路里程、企业信息等完整详细数据
4. 良好的用户交互体验和视觉效果
5. 完整的类型安全和错误处理

组件现已准备就绪，等待后端API开发完成后即可无缝集成。
